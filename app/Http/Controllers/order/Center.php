<?php

namespace App\Http\Controllers\order;

use App\Http\Controllers\admin\Center as AdminCenter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Order模組的中心管理控制器
 *
 * 這個控制器繼承自admin\Center，提供相同的功能，
 * 但在order路由群組下運行，以保持B會員功能管理的一致性。
 */
class Center extends AdminCenter
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 覆寫store方法，修改重導向URL
     */
    public function store(Request $request)
    {
        try {
            // 使用與原始控制器相同的驗證邏輯
            $validatedData = $request->validate([
                'name' => 'required|string|max:64|min:2',
                'center_level_id' => 'required|integer|exists:main_db.center_level,id',
                'status' => 'sometimes|integer|in:0,1'
            ], [
                'name.required' => '中心名稱為必填項目',
                'name.string' => '中心名稱必須為文字',
                'name.max' => '中心名稱不能超過64個字元',
                'name.min' => '中心名稱至少需要2個字元',
                'center_level_id.required' => '中心等級為必填項目',
                'center_level_id.integer' => '中心等級必須為數字',
                'center_level_id.exists' => '選擇的中心等級不存在',
                'status.integer' => '狀態必須為數字',
                'status.in' => '狀態只能為0或1'
            ]);

            // 直接建立中心 (centers表在main_db資料庫)
            $centerId = DB::connection('main_db')->table('centers')->insertGetId([
                'name' => $validatedData['name'],
                'center_level_id' => $validatedData['center_level_id'],
                'status' => $validatedData['status'] ?? 1,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // 記錄操作日誌
            $adminData = session('admin');
            DB::connection('main_db')->table('admin_logs')->insert([
                'admin_id' => $adminData['id'] ?? 0,
                'action' => 'create_center',
                'description' => "建立中心: {$validatedData['name']}",
                'admin_name' => $adminData['name'] ?? null,
                'ip' => $request->ip()
            ]);

            // 重導向到order路由
            return redirect('/order/center/index')->with('success', '中心建立成功');
        } catch (\Exception $e) {
            Log::error('建立中心時發生錯誤', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);
            return redirect('/order/center/index')->with('error', '建立中心時發生錯誤，請稍後再試');
        }
    }

    /**
     * 覆寫show方法，修改重導向URL
     */
    public function show(Request $request)
    {
        $id = $request->get('id');

        if (!$id) {
            return redirect('/order/center/index')->with('error', '參數錯誤');
        }

        try {
            // 直接調用父類的show方法，但捕獲重導向並修改URL
            $response = parent::show($request);

            // 如果是重導向響應，修改URL
            if ($response instanceof \Illuminate\Http\RedirectResponse) {
                $targetUrl = $response->getTargetUrl();
                if (strpos($targetUrl, '/admin/center/index') !== false) {
                    return redirect('/order/center/index')->with('error', '中心不存在或查看失敗');
                }
            }

            return $response;
        } catch (\Exception $e) {
            Log::error('查看中心詳情失敗', [
                'center_id' => $id,
                'error' => $e->getMessage()
            ]);
            return redirect('/order/center/index')->with('error', '查看中心詳情失敗');
        }
    }

    /**
     * 從父類提取驗證邏輯
     */
    protected function validateCenterData(Request $request)
    {
        return $request->validate([
            'name' => 'required|string|max:64',
            'level' => 'required|in:區級,市級,省級',
            'status' => 'required|in:active,inactive'
        ], [
            'name.required' => '中心名稱為必填項目',
            'name.max' => '中心名稱最多64個字元',
            'level.required' => '中心等級為必填項目',
            'level.in' => '中心等級必須為區級、市級或省級',
            'status.required' => '狀態為必填項目',
            'status.in' => '狀態必須為啟用或停用'
        ]);
    }

    /**
     * 從父類提取建立邏輯
     */
    protected function createCenter($validatedData, $request)
    {
        try {
            $centerId = DB::connection('main_db')->table('centers')->insertGetId([
                'name' => $validatedData['name'],
                'level' => $validatedData['level'],
                'status' => $validatedData['status'] === 'active' ? 'active' : 'inactive',
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // 記錄操作日誌
            $adminData = session('admin_data', []);
            DB::connection('main_db')->table('admin_logs')->insert([
                'admin_id' => $adminData['id'] ?? 0,
                'action' => 'create_center',
                'description' => "建立中心: {$validatedData['name']}",
                'admin_name' => $adminData['name'] ?? null,
                'ip' => $request->ip()
            ]);

            return ['success' => true, 'center_id' => $centerId];
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * 從父類提取查詢邏輯
     */
    protected function getCenterData($id)
    {
        // 直接查詢中心資料
        $center = DB::connection('main_db')->table('centers')
            ->select(
                'centers.*',
                DB::raw('(SELECT COUNT(*) FROM center_staff WHERE center_id = centers.id AND status = "active") as active_staff_count')
            )
            ->where('centers.id', $id)
            ->first();

        if (!$center) {
            return null;
        }

        // 取得中心的角色指派資料
        $centerStaff = DB::connection('main_db')->table('center_staff')
            ->leftJoin('center_roles', 'center_staff.role_id', '=', 'center_roles.id')
            ->leftJoin('account', 'center_staff.account_id', '=', 'account.id')
            ->select(
                'center_staff.*',
                'center_roles.name as role_name',
                'center_roles.code as role_code',
                'account.name as account_name',
                'account.account as account_number'
            )
            ->where('center_staff.center_id', $id)
            ->where('center_staff.status', 'active')
            ->get();

        return [
            'center' => $center,
            'staff' => $centerStaff
        ];
    }

    /**
     * 取得現役人員概覽
     */
    public function getActiveStaffOverview(Request $request)
    {
        try {
            $centerId = $request->get('center_id');

            if (!$centerId) {
                return response()->json([
                    'status' => false,
                    'message' => '中心ID為必填項目'
                ], 400);
            }

            // 獲取現役人員資料
            $activeStaff = DB::connection('main_db')->table('center_staff as cs')
                ->join('account as a', 'cs.account_id', '=', 'a.id')
                ->join('center_roles as cr', 'cs.role_id', '=', 'cr.id')
                ->where('cs.center_id', $centerId)
                ->where('cs.active_flag', 1)
                ->select([
                    'cs.role_id',
                    'cr.code as role_code',
                    'cr.name as role_name',
                    'a.id as account_id',
                    'a.number as account_number',
                    'a.name as account_name',
                    'cs.start_at'
                ])
                ->orderBy('cs.role_id')
                ->orderBy('cs.start_at')
                ->get();

            // 按角色分組
            $roleGroups = [];
            foreach ($activeStaff as $staff) {
                $roleCode = $staff->role_code;
                if (!isset($roleGroups[$roleCode])) {
                    $roleGroups[$roleCode] = [];
                }
                $roleGroups[$roleCode][] = [
                    'account_id' => $staff->account_id,
                    'account_number' => $staff->account_number,
                    'account_name' => $staff->account_name,
                    'start_at' => $staff->start_at
                ];
            }

            return response()->json([
                'status' => true,
                'data' => [
                    'total_count' => $activeStaff->count(),
                    'roles' => $roleGroups
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('取得現役人員概覽失敗', [
                'center_id' => $request->get('center_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => '系統錯誤，請稍後再試'
            ], 500);
        }
    }
}
