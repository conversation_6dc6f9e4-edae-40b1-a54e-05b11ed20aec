<?php

namespace App\Http\Controllers\admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

// Services
use App\Services\Center\CenterService;
use App\Services\Center\CenterStaffService;
use App\Services\Center\ValidationService;

// 拔掉kiro的垃圾Request類，直接在控制器中驗證

// Traits
use App\Traits\HandlesValidationErrors;

class Center extends MainController
{
    use HandlesValidationErrors;

    const PER_PAGE_ROWS = 10;
    const SIMPLE_MODE_PAGINATE = false;

    protected $centerService;
    protected $centerStaffService;
    protected $validationService;

    public function __construct()
    {
        parent::__construct();
        $this->centerService = new CenterService();
        $this->centerStaffService = new CenterStaffService();
        $this->validationService = new ValidationService();
    }

    /**
     * 記錄操作日誌
     */
    protected function logOperation(string $operation, array $data = [], string $result = 'success'): void
    {
        Log::info('中心管理操作記錄', [
            'operation' => $operation,
            'result' => $result,
            'admin_id' => session('admin_id'),
            'admin_name' => session('admin_name'),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'session_id' => session()->getId(),
            'timestamp' => now(),
            'data' => $data
        ]);
    }



    /**
     * 顯示中心列表頁面
     */
    public function index(Request $request)
    {
        $this->logOperation('查看中心列表', [
            'filters' => $request->only(['name', 'center_level_id', 'status'])
        ]);

        // 取得篩選參數
        $filters = [
            'name' => $request->get('name'),
            'center_level_id' => $request->get('center_level_id'),
            'status' => $request->get('status')
        ];

        // 取得中心列表
        $centers = $this->centerService->getAllCenters($filters);

        // 取得中心等級選項
        $centerLevels = DB::connection('main_db')->table('center_level')
            ->select('id', 'name')
            ->orderBy('id')
            ->get();

        $this->data['centers'] = $centers;
        $this->data['center_levels'] = $centerLevels;
        $this->data['filters'] = $filters;

        return view('admin.center.index', ['data' => $this->data]);
    }

    /**
     * 顯示新增中心表單
     */
    public function create(Request $request)
    {
        // 取得中心等級選項
        $centerLevels = DB::connection('main_db')->table('center_level')
            ->select('id', 'name')
            ->orderBy('id')
            ->get();

        // 取得角色選項
        $centerRoles = DB::connection('main_db')->table('center_roles')
            ->select('id', 'code', 'name', 'is_singleton')
            ->orderBy('id')
            ->get();

        $this->data['center_levels'] = $centerLevels;
        $this->data['center_roles'] = $centerRoles;

        return view('admin.center.create_simple', ['data' => $this->data]);
    }

    /**
     * 處理新增中心請求
     */
    public function store(Request $request)
    {
        try {
            // 簡單直接的驗證，拔掉kiro的垃圾複雜驗證
            $validatedData = $request->validate([
                'name' => 'required|string|max:64',
                'center_level_id' => 'required|integer|exists:main_db.center_level,id',
                'status' => 'nullable|in:0,1'
            ]);

            // 直接建立中心 (centers表在main_db資料庫)
            $centerId = DB::connection('main_db')->table('centers')->insertGetId([
                'name' => $validatedData['name'],
                'center_level_id' => $validatedData['center_level_id'],
                'status' => $validatedData['status'] ?? 1,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // 取得建立的中心資料
            $center = DB::connection('main_db')->table('centers')->where('id', $centerId)->first();

            // 記錄成功操作
            $adminData = session('admin');
            Log::info('中心建立成功', [
                'center_id' => $center->id,
                'center_name' => $center->name,
                'admin_id' => $adminData['id'] ?? null,
                'admin_name' => $adminData['name'] ?? null,
                'ip' => $request->ip()
            ]);

            // 重導向到中心列表頁面
            return redirect('/admin/center/index')->with('success', '中心建立成功');
        } catch (\Exception $e) {
            Log::error('建立中心時發生錯誤', [
                'error' => $e->getMessage(),
                'data' => $validatedData
            ]);
            return redirect()->back()->withErrors(['error' => '建立中心時發生錯誤，請稍後再試'])->withInput();
        }
    }

    /**
     * 額外的中心建立驗證
     */
    protected function validateCenterCreation(array $data): void
    {
        // 檢查同名中心是否已存在（更嚴格的檢查）
        $existingCenter = DB::connection('main_db')
            ->table('centers')
            ->where('name', $data['name'])
            ->first();

        if ($existingCenter) {
            throw ValidationException::withMessages([
                'name' => ['中心名稱已存在，請使用其他名稱']
            ]);
        }

        // 檢查中心等級是否有效且啟用
        $centerLevel = DB::connection('main_db')
            ->table('center_level')
            ->where('id', $data['center_level_id'])
            ->where('status', 1)
            ->first();

        if (!$centerLevel) {
            throw ValidationException::withMessages([
                'center_level_id' => ['選擇的中心等級無效或已停用']
            ]);
        }

        // 檢查管理員是否有權限建立此等級的中心
        if (!$this->canCreateCenterLevel($data['center_level_id'])) {
            throw ValidationException::withMessages([
                'center_level_id' => ['您沒有權限建立此等級的中心']
            ]);
        }
    }

    /**
     * 檢查管理員是否有權限建立指定等級的中心
     */
    protected function canCreateCenterLevel(int $centerLevelId): bool
    {
        // 這裡可以根據業務需求實作權限檢查邏輯
        // 例如：檢查管理員等級、角色權限等

        $adminId = session('admin_id');
        if (!$adminId) {
            return false;
        }

        // 示例：超級管理員可以建立任何等級的中心
        $admin = DB::connection('main_db')
            ->table('admin')
            ->where('id', $adminId)
            ->first();

        if ($admin && isset($admin->is_super_admin) && $admin->is_super_admin) {
            return true;
        }

        // 示例：一般管理員只能建立特定等級的中心
        $allowedLevels = [1, 2, 3]; // 根據業務需求調整
        return in_array($centerLevelId, $allowedLevels);
    }

    /**
     * 顯示中心詳情
     */
    public function show(Request $request)
    {
        $id = $request->get('id');

        if (!$id) {
            return redirect('/admin/center/index')->with('error', '參數錯誤');
        }

        try {
            // 直接查詢中心資料
            $center = DB::connection('main_db')->table('centers')
                ->leftJoin('center_level', 'centers.center_level_id', '=', 'center_level.id')
                ->select(
                    'centers.*',
                    'center_level.name as level_name'
                )
                ->where('centers.id', $id)
                ->first();

            if (!$center) {
                return redirect('/admin/center/index')->with('error', '中心不存在');
            }

            // 取得中心的角色指派資料
            $centerStaff = DB::connection('main_db')->table('center_staff')
                ->leftJoin('center_roles', 'center_staff.role_id', '=', 'center_roles.id')
                ->leftJoin('account', 'center_staff.account_id', '=', 'account.id')
                ->select(
                    'center_staff.*',
                    'center_roles.name as role_name',
                    'center_roles.code as role_code',
                    'center_roles.is_singleton',
                    'account.number as account_number',
                    'account.name as account_name',
                    'account.email as account_email'
                )
                ->where('center_staff.center_id', $id)
                ->whereNull('center_staff.end_at')
                ->whereNull('center_staff.deleted_at')
                ->orderBy('center_roles.id')
                ->orderBy('center_staff.start_at')
                ->get();

            // 取得所有可指派的角色
            $availableRoles = DB::connection('main_db')->table('center_roles')
                ->where('is_assignable', 1)
                ->orderBy('id')
                ->get();

            $this->data['center'] = $center;
            $this->data['center_staff'] = $centerStaff;
            $this->data['available_roles'] = $availableRoles;

            return view('admin.center.show_simple', ['data' => $this->data]);
        } catch (\Exception $e) {
            Log::error('查看中心詳情失敗', [
                'center_id' => $id,
                'error' => $e->getMessage()
            ]);
            return redirect('/admin/center/index')->with('error', '查看中心詳情失敗');
        }
    }

    /**
     * 顯示編輯中心表單
     */
    public function edit(Request $request)
    {
        $id = $request->get('id');

        if (!$id) {
            $this->error(Lang::get('參數錯誤'));
        }

        try {
            $center = $this->centerService->getCenterWithStaff($id);

            if (!$center) {
                $this->error(Lang::get('中心不存在'));
            }

            // 取得中心等級選項
            $centerLevels = DB::connection('main_db')->table('center_level')
                ->select('id', 'name')
                ->orderBy('id')
                ->get();

            // 取得角色選項
            $centerRoles = DB::connection('main_db')->table('center_roles')
                ->select('id', 'code', 'name', 'is_singleton')
                ->orderBy('id')
                ->get();

            $this->data['center'] = $center;
            $this->data['center_levels'] = $centerLevels;
            $this->data['center_roles'] = $centerRoles;

            return view('admin.center.edit', ['data' => $this->data]);
        } catch (\Exception $e) {
            $this->dumpException($e);
        }
    }

    /**
     * 處理更新中心請求
     */
    public function update(Request $request)
    {
        $id = $request->get('id');

        if (!$id) {
            return $this->handleBusinessError('參數錯誤：缺少中心ID');
        }

        try {
            // 簡單直接的驗證，拔掉kiro的垃圾複雜驗證
            $validatedData = $request->validate([
                'name' => 'required|string|max:64',
                'center_level_id' => 'required|integer|exists:main_db.center_level,id',
                'status' => 'nullable|in:0,1'
            ]);

            // 確保status有預設值
            $validatedData['status'] = $validatedData['status'] ?? 1;

            // 更新中心
            $center = $this->centerService->updateCenter($id, $validatedData);

            // 記錄成功操作
            \Log::info('中心更新成功', [
                'center_id' => $center->id,
                'center_name' => $center->name,
                'changes' => $validatedData,
                'admin_id' => session('admin_id'),
                'admin_name' => session('admin_name'),
                'ip' => $request->ip()
            ]);

            return $this->successResponse(
                '中心更新成功',
                [
                    'id' => $center->id,
                    'name' => $center->name,
                    'center_level_id' => $center->center_level_id,
                    'status' => $center->status
                ],
                '/admin/center/edit?id=' . $id
            );
        } catch (ValidationException $e) {
            return $this->handleValidationError($e, '更新中心');
        } catch (\Exception $e) {
            return $this->handleGeneralError($e, '更新中心', '更新中心時發生錯誤，請稍後再試');
        }
    }

    /**
     * 刪除中心
     * 實作刪除前的安全檢查、刪除確認對話框、刪除操作和錯誤處理、加入刪除操作的日誌記錄
     * 需求: 1.5, 1.6, 6.5, 7.4
     */
    public function destroy(Request $request)
    {
        try {
            // 驗證請求參數
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer|exists:main_db.centers,id'
            ], [
                'id.required' => '中心ID為必填',
                'id.integer' => '中心ID格式無效',
                'id.exists' => '指定的中心不存在'
            ]);

            if ($validator->fails()) {
                if ($request->ajax()) {
                    return response()->json([
                        'status' => false,
                        'message' => $validator->errors()->first(),
                        'errors' => $validator->errors()
                    ], 422);
                }
                $this->error($validator->errors()->first());
            }

            $id = $request->get('id');

            // 取得中心資訊用於日誌記錄
            $center = $this->centerService->getCenterWithStaff($id);

            if (!$center) {
                if ($request->ajax()) {
                    return response()->json([
                        'status' => false,
                        'message' => '中心不存在'
                    ], 404);
                }
                $this->error('中心不存在');
            }

            // 實作刪除前的安全檢查（檢查是否有現役人員）
            if (!$this->centerService->canDeleteCenter($id)) {
                $activeStaffCount = $center->activeStaff->count();
                $errorMessage = "無法刪除：中心「{$center->name}」仍有 {$activeStaffCount} 位現役人員";

                // 記錄刪除失敗的日誌
                \Log::warning('中心刪除失敗：有現役人員', [
                    'center_id' => $id,
                    'center_name' => $center->name,
                    'active_staff_count' => $activeStaffCount,
                    'admin_id' => session('admin_id'),
                    'admin_name' => session('admin_name'),
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]);

                if ($request->ajax()) {
                    return response()->json([
                        'status' => false,
                        'message' => $errorMessage,
                        'data' => [
                            'active_staff_count' => $activeStaffCount,
                            'can_delete' => false
                        ]
                    ], 409);
                }
                $this->error($errorMessage);
            }

            // 執行刪除操作
            $deleted = $this->centerService->deleteCenter($id);

            if ($deleted) {
                // 加入刪除操作的日誌記錄
                \Log::info('中心刪除成功', [
                    'center_id' => $id,
                    'center_name' => $center->name,
                    'center_level' => $center->level->name ?? '未設定',
                    'admin_id' => session('admin_id'),
                    'admin_name' => session('admin_name'),
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'deleted_at' => now()
                ]);

                if ($request->ajax()) {
                    return response()->json([
                        'status' => true,
                        'message' => '中心刪除成功',
                        'data' => [
                            'deleted_center_id' => $id,
                            'deleted_center_name' => $center->name
                        ]
                    ]);
                }
                $this->success('中心刪除成功', '/admin/center/index');
            } else {
                throw new \RuntimeException('刪除操作失敗');
            }
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->ajax()) {
                return response()->json([
                    'status' => false,
                    'message' => '資料驗證失敗',
                    'errors' => $e->errors()
                ], 422);
            }
            $this->error($e->errors()->first());
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Log::error('中心刪除失敗：中心不存在', [
                'center_id' => $id ?? null,
                'admin_id' => session('admin_id'),
                'error' => $e->getMessage()
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'status' => false,
                    'message' => '指定的中心不存在'
                ], 404);
            }
            $this->error('指定的中心不存在');
        } catch (\RuntimeException $e) {
            // 業務邏輯錯誤（如有現役人員）
            \Log::warning('中心刪除失敗：業務邏輯錯誤', [
                'center_id' => $id ?? null,
                'admin_id' => session('admin_id'),
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'status' => false,
                    'message' => $e->getMessage()
                ], 409);
            }
            $this->error($e->getMessage());
        } catch (\Exception $e) {
            // 系統錯誤
            \Log::error('中心刪除失敗：系統錯誤', [
                'center_id' => $id ?? null,
                'admin_id' => session('admin_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip()
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'status' => false,
                    'message' => '刪除時發生系統錯誤，請稍後再試'
                ], 500);
            }
            $this->dumpException($e);
        }
    }

    /**
     * AJAX 會員編號自動完成
     * 實作會員編號自動完成的 AJAX 端點
     * 需求: 5.2, 5.3, 5.4, 5.5
     */
    public function getAccountSuggestions(Request $request)
    {
        try {
            // 簡單直接的驗證，拔掉kiro的垃圾複雜驗證
            $validatedData = $request->validate([
                'query' => 'required|string|min:1|max:100',
                'limit' => 'nullable|integer|min:1|max:100'
            ]);

            $query = trim($validatedData['query']);
            $limit = $validatedData['limit'] ?? 10;

            // 記錄搜尋日誌（用於分析和優化）
            $adminData = session('admin');
            \Log::info('會員搜尋請求', [
                'query' => $query,
                'limit' => $limit,
                'admin_id' => $adminData['id'] ?? null,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // 執行搜尋
            $accounts = $this->centerService->searchAccounts($query, $limit);

            // CenterService已經返回格式化的數據，直接使用
            $formattedAccounts = $accounts->map(function ($account) {
                // $account已經是數組格式
                return [
                    'id' => $account['id'],
                    'number' => $account['number'],
                    'name' => $account['name'] ?? '',
                    'label' => $account['display'] ?? ($account['number'] . ($account['name'] ? ' - ' . $account['name'] : '')),
                    'value' => $account['number'],
                    'status' => $account['status'] ?? 1
                ];
            });

            return response()->json([
                'status' => true,
                'data' => $formattedAccounts,
                'count' => $formattedAccounts->count(),
                'query' => $query,
                'limit' => $limit
            ]);
        } catch (ValidationException $e) {
            return $this->handleValidationError($e, '會員搜尋');
        } catch (\Illuminate\Database\QueryException $e) {
            return $this->handleDatabaseError($e, '會員搜尋');
        } catch (\Exception $e) {
            return $this->handleGeneralError($e, '會員搜尋', '搜尋時發生系統錯誤，請稍後再試');
        }
    }

    /**
     * 指派角色
     * 實作單例角色的衝突處理邏輯和多人角色的新增邏輯
     */
    public function assignRole(Request $request)
    {
        try {
            // 簡單直接的驗證，拔掉kiro的垃圾複雜驗證
            $validatedData = $request->validate([
                'center_id' => 'required|integer|exists:main_db.centers,id',
                'account_number' => 'required|string|exists:main_db.account,number',
                'role_code' => 'required|string|in:founder,director,executive_director,lecturer,market,sales',
                'start_at' => 'nullable|date',
                'note' => 'nullable|string|max:255'
            ]);

            $centerId = $validatedData['center_id'];
            $accountNumber = $validatedData['account_number'];
            $roleCode = $validatedData['role_code'];
            $startAt = isset($validatedData['start_at']) && $validatedData['start_at'] ? \Illuminate\Support\Carbon::parse($validatedData['start_at']) : null;

            // 根據會員編號查找會員ID
            $account = DB::connection('main_db')->table('account')
                ->where('number', $accountNumber)
                ->first();

            if (!$account) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到會員編號：' . $accountNumber
                ], 404);
            }

            $accountId = $account->id;

            // 檢查是否為單例角色並處理衝突
            $role = DB::connection('main_db')->table('center_roles')
                ->where('code', $roleCode)
                ->first();

            if (!$role) {
                return response()->json([
                    'status' => false,
                    'message' => '角色不存在'
                ], 404);
            }

            // 檢查該會員是否已經在此中心擔任此角色
            $existingActiveRole = $this->centerStaffService->getActiveStaff($centerId, $roleCode)
                ->where('account_id', $accountId)
                ->first();

            if ($existingActiveRole) {
                return response()->json([
                    'status' => false,
                    'message' => '該會員已在此中心擔任此角色'
                ], 409);
            }

            // 單例角色衝突處理邏輯
            if ($role->is_singleton) {
                $hasActiveSingleton = $this->centerStaffService->hasActiveSingleton($centerId, $roleCode);

                if ($hasActiveSingleton) {
                    // 自動結束現有的單例角色並指派新角色
                    $result = $this->centerStaffService->assignRole(
                        $centerId,
                        $accountId,
                        $roleCode,
                        $startAt,
                        true // 自動結束現有單例角色
                    );

                    // 取得會員資訊用於回傳
                    $account = DB::connection('main_db')->table('account')
                        ->where('id', $accountId)
                        ->select('id', 'number', 'name')
                        ->first();

                    return response()->json([
                        'status' => true,
                        'message' => '角色指派成功（已自動結束前任）',
                        'data' => [
                            'id' => $result->id,
                            'center_id' => $result->center_id,
                            'account_id' => $result->account_id,
                            'role_code' => $roleCode,
                            'start_at' => $result->start_at,
                            'account' => $account,
                            'role_name' => $role->name,
                            'is_singleton' => true,
                            'replaced_existing' => true
                        ]
                    ]);
                }
            }

            // 多人角色的新增邏輯或無衝突的單例角色指派
            $result = $this->centerStaffService->assignRole(
                $centerId,
                $accountId,
                $roleCode,
                $startAt,
                false // 不自動結束（多人角色或無衝突情況）
            );

            // 取得會員資訊用於回傳
            $account = DB::connection('main_db')->table('account')
                ->where('id', $accountId)
                ->select('id', 'number', 'name')
                ->first();

            return response()->json([
                'status' => true,
                'message' => '角色指派成功',
                'data' => [
                    'id' => $result->id,
                    'center_id' => $result->center_id,
                    'account_id' => $result->account_id,
                    'role_code' => $roleCode,
                    'start_at' => $result->start_at,
                    'account' => $account,
                    'role_name' => $role->name,
                    'is_singleton' => (bool)$role->is_singleton,
                    'replaced_existing' => false
                ]
            ]);
        } catch (ValidationException $e) {
            return $this->handleValidationError($e, '角色指派');
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->handleBusinessError('指定的資源不存在', [], 404);
        } catch (\RuntimeException $e) {
            return $this->handleBusinessError($e->getMessage(), [], 409);
        } catch (\Exception $e) {
            return $this->handleGeneralError($e, '角色指派', '角色指派時發生系統錯誤，請稍後再試');
        }
    }

    /**
     * 移除角色
     * 實作多人角色的移除邏輯，不影響其他同角色人員
     * 支援兩種參數方式：
     * 1. staff_id - 直接指定員工記錄ID
     * 2. center_id + account_id + role_code - 通過組合查找員工記錄
     */
    public function removeRole(Request $request)
    {
        try {
            $data = $request->all();

            // 支援兩種參數方式的驗證
            if (isset($data['staff_id'])) {
                // 方式1：直接使用 staff_id
                $validator = Validator::make($data, [
                    'staff_id' => 'required|integer|exists:main_db.center_staff,id',
                    'note' => 'nullable|string|max:255'
                ], [
                    'staff_id.required' => '員工記錄ID為必填',
                    'staff_id.exists' => '指定的員工記錄不存在',
                    'note.max' => '備註不能超過255個字元'
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'status' => false,
                        'message' => $validator->errors()->first(),
                        'errors' => $validator->errors()
                    ], 422);
                }

                $staffId = $data['staff_id'];
            } else {
                // 方式2：使用 center_id + account_id + role_code 組合
                $validator = Validator::make($data, [
                    'center_id' => 'required|integer|exists:main_db.centers,id',
                    'account_id' => 'required|integer|exists:main_db.account,id',
                    'role_code' => 'required|string|in:founder,director,executive_director,lecturer,market,sales',
                    'note' => 'nullable|string|max:255'
                ], [
                    'center_id.required' => '中心ID為必填',
                    'center_id.exists' => '指定的中心不存在',
                    'account_id.required' => '會員ID為必填',
                    'account_id.exists' => '指定的會員不存在',
                    'role_code.required' => '角色代碼為必填',
                    'role_code.in' => '無效的角色代碼',
                    'note.max' => '備註不能超過255個字元'
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'status' => false,
                        'message' => $validator->errors()->first(),
                        'errors' => $validator->errors()
                    ], 422);
                }

                // 查找對應的 staff_id
                $staffRecord = DB::connection('main_db')->table('center_staff')
                    ->leftJoin('center_roles', 'center_staff.role_id', '=', 'center_roles.id')
                    ->where('center_staff.center_id', $data['center_id'])
                    ->where('center_staff.account_id', $data['account_id'])
                    ->where('center_roles.code', $data['role_code'])
                    ->whereNull('center_staff.end_at')
                    ->select('center_staff.id')
                    ->first();

                if (!$staffRecord) {
                    return response()->json([
                        'status' => false,
                        'message' => '找不到對應的角色記錄，該會員可能未在此中心擔任此角色'
                    ], 404);
                }

                $staffId = $staffRecord->id;
            }

            $note = $data['note'] ?? null;

            // 查找員工記錄
            $staff = DB::connection('main_db')->table('center_staff')
                ->leftJoin('center_roles', 'center_staff.role_id', '=', 'center_roles.id')
                ->leftJoin('account', 'center_staff.account_id', '=', 'account.id')
                ->select(
                    'center_staff.*',
                    'center_roles.code as role_code',
                    'center_roles.name as role_name',
                    'account.number as account_number',
                    'account.name as account_name'
                )
                ->where('center_staff.id', $staffId)
                ->whereNull('center_staff.end_at')
                ->whereNull('center_staff.deleted_at')
                ->first();

            if (!$staff) {
                return response()->json([
                    'status' => false,
                    'message' => '找不到現役的員工記錄'
                ], 404);
            }

            // 結束角色指派
            $endAt = now();
            $updateData = [
                'end_at' => $endAt,
                'updated_at' => now()
            ];

            if ($note) {
                $updateData['note'] = ($staff->note ? $staff->note . ' | ' : '') . "結束: {$note}";
            }

            $updated = DB::connection('main_db')->table('center_staff')
                ->where('id', $staffId)
                ->update($updateData);

            if (!$updated) {
                return response()->json([
                    'status' => false,
                    'message' => '移除角色失敗'
                ], 500);
            }

            // 記錄日誌
            \Log::info('角色移除成功', [
                'staff_id' => $staffId,
                'center_id' => $staff->center_id,
                'account_id' => $staff->account_id,
                'account_number' => $staff->account_number,
                'account_name' => $staff->account_name,
                'role_code' => $staff->role_code,
                'role_name' => $staff->role_name,
                'end_at' => $endAt
            ]);

            return response()->json([
                'status' => true,
                'message' => '角色移除成功',
                'data' => [
                    'removed_staff_id' => $staffId,
                    'center_id' => $staff->center_id,
                    'account_id' => $staff->account_id,
                    'role_code' => $staff->role_code,
                    'end_at' => $endAt,
                    'account' => [
                        'id' => $staff->account_id,
                        'number' => $staff->account_number,
                        'name' => $staff->account_name
                    ],
                    'role_name' => $staff->role_name
                ]
            ]);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'status' => false,
                'message' => '指定的資源不存在'
            ], 404);
        } catch (\Exception $e) {
            \Log::error('角色移除失敗', [
                'staff_id' => $data['staff_id'] ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'message' => '角色移除時發生系統錯誤，請稍後再試'
            ], 500);
        }
    }

    /**
     * 檢查中心是否可以刪除
     * 提供 AJAX 端點用於前端驗證
     */
    public function canDelete(Request $request, $id)
    {
        try {
            $validator = Validator::make(['id' => $id], [
                'id' => 'required|integer|exists:main_db.centers,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => '中心不存在',
                    'can_delete' => false
                ], 404);
            }

            $center = $this->centerService->getCenterWithStaff($id);
            $canDelete = $this->centerService->canDeleteCenter($id);
            $activeStaffCount = $center->activeStaff->count();

            return response()->json([
                'status' => true,
                'can_delete' => $canDelete,
                'data' => [
                    'center_id' => $id,
                    'center_name' => $center->name,
                    'active_staff_count' => $activeStaffCount,
                    'message' => $canDelete
                        ? '可以刪除此中心'
                        : "無法刪除：中心仍有 {$activeStaffCount} 位現役人員"
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '檢查失敗：' . $e->getMessage(),
                'can_delete' => false
            ], 500);
        }
    }

    /**
     * 切換中心狀態
     */
    public function toggleStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'id' => 'required|integer|exists:main_db.centers,id',
                'status' => 'required|integer|in:0,1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first()
                ]);
            }

            $center = DB::connection('main_db')->table('centers')
                ->where('id', $request->get('id'))
                ->update(['status' => $request->get('status')]);

            return response()->json([
                'status' => true,
                'message' => Lang::get('操作成功')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量更新狀態
     */
    public function batchStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:main_db.centers,id',
                'status' => 'required|integer|in:0,1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first()
                ]);
            }

            DB::connection('main_db')->table('centers')
                ->whereIn('id', $request->get('ids'))
                ->update(['status' => $request->get('status')]);

            return response()->json([
                'status' => true,
                'message' => Lang::get('操作成功')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量刪除中心
     * 實作批量刪除前的安全檢查和日誌記錄
     */
    public function batchDelete(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'ids' => 'required|array|min:1',
                'ids.*' => 'integer|exists:main_db.centers,id'
            ], [
                'ids.required' => '請選擇要刪除的中心',
                'ids.array' => '中心ID格式無效',
                'ids.min' => '至少需要選擇一個中心',
                'ids.*.integer' => '中心ID必須為整數',
                'ids.*.exists' => '選擇的中心不存在'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }

            $ids = $request->get('ids');
            $failed = [];
            $deleted = [];
            $centerDetails = [];

            // 記錄批量刪除開始
            \Log::info('批量刪除中心開始', [
                'center_ids' => $ids,
                'total_count' => count($ids),
                'admin_id' => session('admin_id'),
                'admin_name' => session('admin_name'),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            foreach ($ids as $id) {
                try {
                    // 取得中心資訊用於日誌
                    $center = $this->centerService->getCenterWithStaff($id);

                    if (!$center) {
                        $failed[] = [
                            'id' => $id,
                            'reason' => '中心不存在'
                        ];
                        continue;
                    }

                    // 檢查是否可以刪除
                    if ($this->centerService->canDeleteCenter($id)) {
                        $this->centerService->deleteCenter($id);
                        $deleted[] = $id;

                        // 記錄成功刪除的中心
                        $centerDetails[] = [
                            'id' => $id,
                            'name' => $center->name,
                            'level' => $center->level->name ?? '未設定'
                        ];

                        \Log::info('批量刪除中心成功', [
                            'center_id' => $id,
                            'center_name' => $center->name,
                            'admin_id' => session('admin_id')
                        ]);
                    } else {
                        $activeStaffCount = $center->activeStaff->count();
                        $failed[] = [
                            'id' => $id,
                            'name' => $center->name,
                            'reason' => "有 {$activeStaffCount} 位現役人員"
                        ];

                        \Log::warning('批量刪除中心失敗：有現役人員', [
                            'center_id' => $id,
                            'center_name' => $center->name,
                            'active_staff_count' => $activeStaffCount,
                            'admin_id' => session('admin_id')
                        ]);
                    }
                } catch (\Exception $e) {
                    $failed[] = [
                        'id' => $id,
                        'reason' => '系統錯誤：' . $e->getMessage()
                    ];

                    \Log::error('批量刪除中心失敗：系統錯誤', [
                        'center_id' => $id,
                        'error' => $e->getMessage(),
                        'admin_id' => session('admin_id')
                    ]);
                }
            }

            // 記錄批量刪除結果
            \Log::info('批量刪除中心完成', [
                'total_requested' => count($ids),
                'successful_count' => count($deleted),
                'failed_count' => count($failed),
                'deleted_centers' => $centerDetails,
                'failed_centers' => $failed,
                'admin_id' => session('admin_id'),
                'admin_name' => session('admin_name')
            ]);

            $message = '批量刪除完成';
            if (count($failed) > 0) {
                $message .= "，成功刪除 " . count($deleted) . " 個中心，" . count($failed) . " 個中心無法刪除";
            } else {
                $message .= "，成功刪除 " . count($deleted) . " 個中心";
            }

            return response()->json([
                'status' => true,
                'message' => $message,
                'data' => [
                    'deleted' => $deleted,
                    'failed' => $failed,
                    'deleted_count' => count($deleted),
                    'failed_count' => count($failed),
                    'center_details' => $centerDetails
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('批量刪除中心失敗：系統錯誤', [
                'center_ids' => $request->get('ids', []),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'admin_id' => session('admin_id'),
                'ip' => $request->ip()
            ]);

            return response()->json([
                'status' => false,
                'message' => '批量刪除時發生系統錯誤，請稍後再試'
            ], 500);
        }
    }

    /**
     * 批量指派角色
     * 支援為多個會員同時指派相同角色（僅限多人角色）
     */
    public function batchAssignRole(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'center_id' => 'required|integer|exists:main_db.centers,id',
                'account_ids' => 'required|array|min:1',
                'account_ids.*' => 'integer|exists:main_db.account,id',
                'role_code' => 'required|string|in:founder,director,executive_director,lecturer,market,sales',
                'start_at' => 'nullable|date',
                'note' => 'nullable|string|max:255'
            ], [
                'center_id.required' => '中心ID為必填',
                'account_ids.required' => '會員ID列表為必填',
                'account_ids.min' => '至少需要選擇一個會員',
                'role_code.required' => '角色代碼為必填',
                'role_code.in' => '無效的角色代碼'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }

            $centerId = $request->get('center_id');
            $accountIds = $request->get('account_ids');
            $roleCode = $request->get('role_code');
            $startAt = $request->get('start_at') ? \Carbon\Carbon::parse($request->get('start_at')) : null;

            // 檢查角色是否為單例角色
            $role = DB::connection('main_db')->table('center_roles')
                ->where('code', $roleCode)
                ->first();

            if (!$role) {
                return response()->json([
                    'status' => false,
                    'message' => '角色不存在'
                ], 404);
            }

            if ($role->is_singleton && count($accountIds) > 1) {
                return response()->json([
                    'status' => false,
                    'message' => '單例角色不支援批量指派，請逐一指派'
                ], 400);
            }

            $successful = [];
            $failed = [];

            foreach ($accountIds as $accountId) {
                try {
                    // 檢查該會員是否已經在此中心擔任此角色
                    $existingActiveRole = $this->centerStaffService->getActiveStaff($centerId, $roleCode)
                        ->where('account_id', $accountId)
                        ->first();

                    if ($existingActiveRole) {
                        $failed[] = [
                            'account_id' => $accountId,
                            'reason' => '該會員已在此中心擔任此角色'
                        ];
                        continue;
                    }

                    $result = $this->centerStaffService->assignRole(
                        $centerId,
                        $accountId,
                        $roleCode,
                        $startAt,
                        false
                    );

                    $successful[] = [
                        'account_id' => $accountId,
                        'staff_id' => $result->id
                    ];
                } catch (\Exception $e) {
                    $failed[] = [
                        'account_id' => $accountId,
                        'reason' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'status' => true,
                'message' => '批量角色指派完成',
                'data' => [
                    'successful' => $successful,
                    'failed' => $failed,
                    'successful_count' => count($successful),
                    'failed_count' => count($failed)
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('批量角色指派失敗', [
                'center_id' => $request->get('center_id'),
                'account_ids' => $request->get('account_ids'),
                'role_code' => $request->get('role_code'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => false,
                'message' => '批量角色指派時發生系統錯誤'
            ], 500);
        }
    }

    /**
     * 批量移除角色
     * 支援同時移除多個會員的相同角色
     */
    public function batchRemoveRole(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'center_id' => 'required|integer|exists:main_db.centers,id',
                'account_ids' => 'required|array|min:1',
                'account_ids.*' => 'integer|exists:main_db.account,id',
                'role_code' => 'required|string|in:founder,director,executive_director,lecturer,market,sales',
                'end_at' => 'nullable|date'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }

            $centerId = $request->get('center_id');
            $accountIds = $request->get('account_ids');
            $roleCode = $request->get('role_code');
            $endAt = $request->get('end_at') ? \Carbon\Carbon::parse($request->get('end_at')) : null;

            $successful = [];
            $failed = [];

            foreach ($accountIds as $accountId) {
                try {
                    $endedStaff = $this->centerStaffService->endActiveRole(
                        $centerId,
                        $accountId,
                        $roleCode,
                        $endAt
                    );

                    if ($endedStaff) {
                        $successful[] = [
                            'account_id' => $accountId,
                            'staff_id' => $endedStaff->id
                        ];
                    } else {
                        $failed[] = [
                            'account_id' => $accountId,
                            'reason' => '該會員目前未在此中心擔任此角色'
                        ];
                    }
                } catch (\Exception $e) {
                    $failed[] = [
                        'account_id' => $accountId,
                        'reason' => $e->getMessage()
                    ];
                }
            }

            return response()->json([
                'status' => true,
                'message' => '批量角色移除完成',
                'data' => [
                    'successful' => $successful,
                    'failed' => $failed,
                    'successful_count' => count($successful),
                    'failed_count' => count($failed)
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('批量角色移除失敗', [
                'center_id' => $request->get('center_id'),
                'account_ids' => $request->get('account_ids'),
                'role_code' => $request->get('role_code'),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'status' => false,
                'message' => '批量角色移除時發生系統錯誤'
            ], 500);
        }
    }

    /**
     * 驗證角色指派
     * 檢查指定的角色指派是否有效
     */
    public function validateRoleAssignment(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'center_id' => 'required|integer|exists:main_db.centers,id',
                'account_id' => 'required|integer|exists:main_db.account,id',
                'role_code' => 'required|string|in:founder,director,executive_director,lecturer,market,sales'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first(),
                    'errors' => $validator->errors()
                ], 422);
            }

            $centerId = $request->get('center_id');
            $accountId = $request->get('account_id');
            $roleCode = $request->get('role_code');

            // 取得角色資訊
            $role = DB::connection('main_db')->table('center_roles')
                ->where('code', $roleCode)
                ->first();

            if (!$role) {
                return response()->json([
                    'status' => false,
                    'message' => '角色不存在',
                    'valid' => false
                ]);
            }

            $validationResult = [
                'valid' => true,
                'warnings' => [],
                'conflicts' => []
            ];

            // 檢查該會員是否已經在此中心擔任此角色
            $existingActiveRole = $this->centerStaffService->getActiveStaff($centerId, $roleCode)
                ->where('account_id', $accountId)
                ->first();

            if ($existingActiveRole) {
                $validationResult['valid'] = false;
                $validationResult['conflicts'][] = '該會員已在此中心擔任此角色';
            }

            // 檢查單例角色衝突
            if ($role->is_singleton) {
                $hasActiveSingleton = $this->centerStaffService->hasActiveSingleton($centerId, $roleCode);

                if ($hasActiveSingleton) {
                    $currentSingleton = $this->centerStaffService->getActiveStaff($centerId, $roleCode)->first();
                    if ($currentSingleton && $currentSingleton->account_id !== $accountId) {
                        $currentAccount = DB::connection('main_db')->table('account')
                            ->where('id', $currentSingleton->account_id)
                            ->select('number', 'name')
                            ->first();

                        $validationResult['warnings'][] = [
                            'type' => 'singleton_conflict',
                            'message' => '此角色目前由其他會員擔任，指派後將自動結束其任期',
                            'current_holder' => [
                                'account_id' => $currentSingleton->account_id,
                                'number' => $currentAccount->number ?? '',
                                'name' => $currentAccount->name ?? ''
                            ]
                        ];
                    }
                }
            }

            return response()->json([
                'status' => true,
                'data' => $validationResult
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '驗證角色指派時發生錯誤'
            ], 500);
        }
    }

    /**
     * 取得中心角色狀態
     * 實作動態介面更新邏輯的支援端點
     * 需求: 5.2, 5.3, 5.4, 5.5
     */
    public function getRoleStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'center_id' => 'required|integer|exists:main_db.centers,id',
                'role_codes' => 'nullable|array',
                'role_codes.*' => 'string|in:founder,director,executive_director,lecturer,market,sales'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first()
                ], 422);
            }

            $centerId = $request->get('center_id');
            $roleCodes = $request->get('role_codes', []);

            // 如果沒有指定角色，取得所有角色
            if (empty($roleCodes)) {
                $roleCodes = ['founder', 'director', 'executive_director', 'lecturer', 'market', 'sales'];
            }

            $roleStatus = [];

            foreach ($roleCodes as $roleCode) {
                // 取得角色資訊
                $role = DB::connection('main_db')->table('center_roles')
                    ->where('code', $roleCode)
                    ->first();

                if (!$role) {
                    continue;
                }

                // 取得現役人員
                $activeStaff = $this->centerStaffService->getActiveStaff($centerId, $roleCode);

                $staffList = $activeStaff->map(function ($staff) {
                    $account = DB::connection('main_db')->table('account')
                        ->where('id', $staff->account_id)
                        ->select('id', 'number', 'name')
                        ->first();

                    return [
                        'staff_id' => $staff->id,
                        'account_id' => $staff->account_id,
                        'account_number' => $account->number ?? '',
                        'account_name' => $account->name ?? '',
                        'start_at' => $staff->start_at,
                        'note' => $staff->note
                    ];
                });

                $roleStatus[$roleCode] = [
                    'role_name' => $role->name,
                    'is_singleton' => (bool)$role->is_singleton,
                    'active_count' => $activeStaff->count(),
                    'staff_list' => $staffList,
                    'can_assign_more' => !$role->is_singleton || $activeStaff->count() === 0
                ];
            }

            return response()->json([
                'status' => true,
                'data' => [
                    'center_id' => $centerId,
                    'roles' => $roleStatus,
                    'updated_at' => now()->toISOString()
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('取得角色狀態失敗', [
                'center_id' => $request->get('center_id'),
                'error' => $e->getMessage(),
                'admin_id' => session('admin_id')
            ]);

            return response()->json([
                'status' => false,
                'message' => '取得角色狀態時發生錯誤'
            ], 500);
        }
    }
}
