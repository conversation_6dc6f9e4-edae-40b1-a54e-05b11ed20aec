@extends('admin.Public.aside')
@section('title')編輯中心@endsection

@section('ownCSS')
    {{-- jQ<PERSON>y UI CSS for autocomplete --}}
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
    {{-- Bootstrap Icons --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    {{-- 中心驗證系統樣式 --}}
    <link rel="stylesheet" href="/static/admin/css/center-validation.css">
    {{-- UX 增強樣式 --}}
    <link rel="stylesheet" href="/static/admin/css/center-ux-enhancements.css">
    {{-- CSRF Token for AJAX requests --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">中心管理</a></li>
            <li><a href="###" onclick="javascript:location.href='/order/center/index'">中心列表</a></li>
            <li><a href="###">編輯中心</a></li>
        </ul>

        <div class="edit_form">
            <form method="POST" action="/order/center/update" id="centerForm"
                  data-loading-form="true"
                  data-track-changes="true"
                  data-show-progress="true">
                @csrf
                {{-- Hidden field for center ID --}}
                <input type="hidden" name="id" id="center_id" value="{{ $data['center']->id }}">

                @if(isset($errors) && $errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger">
                        {{ session('error') }}
                    </div>
                @endif

                @if(session('success'))
                    <div class="alert alert-success">
                        {{ session('success') }}
                    </div>
                @endif

                <!-- 中心基本資訊 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">中心基本資訊</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">中心名稱 <span class="text-danger">*</span></label>
                                    <input type="text"
                                           class="form-control"
                                           id="name"
                                           name="name"
                                           value="{{ old('name', $data['center']->name) }}"
                                           placeholder="請輸入中心名稱"
                                           maxlength="64"
                                           data-validate="centerName"
                                           required>
                                    <small class="form-text text-muted">最多64個字元</small>
                                    <div class="invalid-feedback" id="name-error"></div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="center_level_id">中心等級 <span class="text-danger">*</span></label>
                                    <select class="form-control"
                                            id="center_level_id"
                                            name="center_level_id"
                                            data-validate="centerLevel"
                                            required>
                                        <option value="">請選擇中心等級</option>
                                        @foreach($data['center_levels'] as $level)
                                            <option value="{{ $level->id }}"
                                                    {{ old('center_level_id', $data['center']->center_level_id) == $level->id ? 'selected' : '' }}>
                                                {{ $level->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    <div class="invalid-feedback" id="center_level_id-error"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">狀態</label>
                                    <select class="form-control"
                                            id="status"
                                            name="status"
                                            data-validate="status">
                                        <option value="1" {{ old('status', $data['center']->status) == '1' ? 'selected' : '' }}>啟用</option>
                                        <option value="0" {{ old('status', $data['center']->status) == '0' ? 'selected' : '' }}>停用</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>建立時間</label>
                                    <input type="text"
                                           class="form-control"
                                           value="{{ $data['center']->created_at ? \Carbon\Carbon::parse($data['center']->created_at)->format('Y-m-d H:i:s') : '' }}"
                                           readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 現役人員概覽 -->
                @if(isset($data['center']->activeStaff) && count($data['center']->activeStaff) > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-people"></i> 現役人員概覽
                                <span class="badge badge-info">{{ count($data['center']->activeStaff) }}人</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @php
                                    $staffByRole = collect($data['center']->activeStaff)->groupBy(function($staff) {
                                        return $staff->role->code ?? 'unknown';
                                    });
                                @endphp

                                @foreach($data['center_roles'] as $role)
                                    @php
                                        $roleStaff = $staffByRole->get($role->code, collect());
                                    @endphp
                                    <div class="col-md-4 mb-3">
                                        <div class="staff-summary">
                                            <h6 class="text-muted">{{ $role->name }}</h6>
                                            @if($roleStaff->count() > 0)
                                                <div class="staff-list">
                                                    @foreach($roleStaff as $staff)
                                                        <div class="staff-item">
                                                            <span class="badge badge-success">
                                                                {{ $staff->account->number ?? '' }}
                                                                {{ $staff->account->name ? ' - ' . $staff->account->name : '' }}
                                                            </span>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @else
                                                <span class="text-muted">
                                                    <small>尚未指派</small>
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- 角色指派管理 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-gear"></i> 角色指派管理
                        </h5>
                        <small class="text-muted">管理中心的各種角色人員配置</small>
                    </div>
                    <div class="card-body">
                        @include('admin.center.components.role-assignment', ['center' => $data['center']])
                    </div>
                </div>

                <!-- 操作按鈕 -->
                <div class="form-group">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="bi bi-check-lg"></i> 更新中心
                    </button>
                    <a href="/order/center/show?id={{ $data['center']->id }}" class="btn btn-info">
                        <i class="bi bi-eye"></i> 查看詳情
                    </a>
                    <a href="/order/center/index" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>

                    @if(isset($data['center']) && $data['center'])
                        <button type="button" class="btn btn-danger float-right" id="deleteBtn"
                                data-center-id="{{ $data['center']->id }}"
                                data-center-name="{{ $data['center']->name }}">
                            <i class="bi bi-trash"></i> 刪除中心
                        </button>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- 刪除確認模態框 -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="bi bi-exclamation-triangle text-warning"></i> 確認刪除
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>您確定要刪除中心 <strong id="deleteCenterName"></strong> 嗎？</p>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>注意：</strong>此操作無法復原。如果中心有現役人員，將無法刪除。
                    </div>
                    <div id="deleteWarnings" class="alert alert-danger" style="display: none;">
                        <!-- 動態顯示刪除警告 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                        <i class="bi bi-trash"></i> 確認刪除
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    {{-- jQuery UI JavaScript for autocomplete --}}
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    {{-- UX 增強系統 --}}
    <script src="/static/admin/js/center-ux-enhancements.js"></script>
    {{-- 中心驗證系統 --}}
    <script src="/static/admin/js/center-validation.js"></script>

    <script>
        $(document).ready(function() {
            // 初始化表單驗證
            $('#centerForm').validateCenter({
                fields: {
                    '#name': 'centerName',
                    '#center_level_id': 'centerLevel',
                    '#status': 'status'
                }
            });

            // 表單提交處理（覆蓋預設行為以使用 AJAX）
            $('#centerForm').off('submit').on('submit', function(e) {
                e.preventDefault();

                // 使用統一驗證系統
                if (!window.centerValidation.validateForm($(this))) {
                    return false;
                }

                // 提交表單
                submitForm();
            });

            // 增強的即時驗證
            $('#name').on('input', function() {
                const $this = $(this);
                const name = $this.val().trim();

                // 即時長度檢查
                if (name.length > 64) {
                    window.centerValidation.showFieldError($this, '中心名稱不能超過64個字元');
                } else if (name.length > 0 && name.length < 2) {
                    window.centerValidation.showFieldError($this, '中心名稱至少需要2個字元');
                } else {
                    window.centerValidation.clearFieldError($this);
                }

                // 更新字元計數
                updateCharacterCount('name', name.length, 64);
            });

            // 字元計數顯示
            function updateCharacterCount(fieldId, current, max) {
                let $counter = $(`#${fieldId}-counter`);
                if ($counter.length === 0) {
                    $counter = $(`<small class="form-text text-muted" id="${fieldId}-counter"></small>`);
                    $(`#${fieldId}`).after($counter);
                }

                const remaining = max - current;
                const color = remaining < 10 ? 'text-warning' : (remaining < 5 ? 'text-danger' : 'text-muted');
                $counter.removeClass('text-muted text-warning text-danger').addClass(color);
                $counter.text(`${current}/${max} 字元`);
            }

            // 刪除按鈕事件
            $('#deleteBtn').on('click', function() {
                const centerId = $(this).data('center-id');
                const centerName = $(this).data('center-name');

                $('#deleteCenterName').text(centerName);
                $('#deleteModal').modal('show');
            });

            // 確認刪除事件
            $('#confirmDeleteBtn').on('click', function() {
                const centerId = $('#deleteBtn').data('center-id');
                deleteCenter(centerId);
            });
        });

        // 顯示欄位錯誤
        function showFieldError(fieldName, message) {
            const field = $('#' + fieldName);
            const errorDiv = $('#' + fieldName + '-error');

            field.addClass('is-invalid');
            errorDiv.text(message);
        }

        // 清除欄位錯誤
        function clearFieldError(fieldName) {
            const field = $('#' + fieldName);
            const errorDiv = $('#' + fieldName + '-error');

            field.removeClass('is-invalid');
            errorDiv.text('');
        }

        // 提交表單
        function submitForm() {
            const submitBtn = $('#submitBtn');
            const originalText = submitBtn.html();

            // 顯示載入狀態
            submitBtn.prop('disabled', true)
                     .html('<i class="bi bi-hourglass-split"></i> 更新中...');

            $.ajax({
                url: '/order/center/update',
                method: 'POST',
                data: $('#centerForm').serialize(),
                success: function(response) {
                    // 顯示成功訊息
                    showSuccessMessage('中心更新成功！');

                    // 延遲後重新載入頁面以顯示最新資料
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                },
                error: function(xhr) {
                    // 恢復按鈕狀態
                    submitBtn.prop('disabled', false).html(originalText);

                    if (xhr.status === 422) {
                        // 驗證錯誤
                        const errors = xhr.responseJSON.errors || {};

                        // 顯示驗證錯誤
                        Object.keys(errors).forEach(function(field) {
                            if (errors[field] && errors[field].length > 0) {
                                showFieldError(field, errors[field][0]);
                            }
                        });

                        // 顯示通用錯誤訊息
                        if (xhr.responseJSON.message) {
                            showErrorMessage('更新失敗：' + xhr.responseJSON.message);
                        }
                    } else {
                        // 其他錯誤
                        const message = xhr.responseJSON?.message || '更新中心時發生錯誤，請稍後再試';
                        showErrorMessage('更新失敗：' + message);
                    }
                }
            });
        }

        // 刪除中心
        function deleteCenter(centerId) {
            const confirmBtn = $('#confirmDeleteBtn');
            const originalText = confirmBtn.html();

            // 顯示載入狀態
            confirmBtn.prop('disabled', true)
                      .html('<i class="bi bi-hourglass-split"></i> 刪除中...');

            $.ajax({
                url: '/order/center/destroy',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    id: centerId
                },
                success: function(response) {
                    $('#deleteModal').modal('hide');

                    // 顯示成功訊息
                    showSuccessMessage('中心刪除成功！');

                    // 延遲後重導向到列表頁面
                    setTimeout(function() {
                        window.location.href = '/order/center/index';
                    }, 1500);
                },
                error: function(xhr) {
                    // 恢復按鈕狀態
                    confirmBtn.prop('disabled', false).html(originalText);

                    let message = '刪除中心時發生錯誤';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        message = xhr.responseJSON.message;
                    }

                    // 在模態框中顯示錯誤
                    $('#deleteWarnings').html(`
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>刪除失敗：</strong>${message}
                    `).show();
                }
            });
        }

        // 顯示成功訊息
        function showSuccessMessage(message) {
            if (typeof toastr !== 'undefined') {
                toastr.success(message);
            } else {
                showNotification(message, 'success');
            }
        }

        // 顯示錯誤訊息
        function showErrorMessage(message) {
            if (typeof toastr !== 'undefined') {
                toastr.error(message);
            } else {
                showNotification(message, 'error');
            }
        }
    </script>
@endsection

<style>
.staff-summary {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.staff-summary h6 {
    margin-bottom: 8px;
    font-weight: 600;
}

.staff-item {
    margin-bottom: 4px;
}

.staff-item .badge {
    font-size: 0.8em;
    padding: 4px 8px;
}

.card-header h5 {
    margin-bottom: 0;
}

.card-header small {
    display: block;
    margin-top: 4px;
}

.modal-body .alert {
    margin-bottom: 15px;
}

.modal-body .alert:last-child {
    margin-bottom: 0;
}

#deleteWarnings {
    margin-top: 15px;
}

.float-right {
    float: right !important;
}

/* 響應式調整 */
@media (max-width: 768px) {
    .float-right {
        float: none !important;
        margin-top: 10px;
        width: 100%;
    }

    .staff-summary {
        margin-bottom: 15px;
    }
}
</style>
