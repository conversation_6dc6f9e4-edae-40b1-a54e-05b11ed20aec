@extends('admin.Public.aside')
@section('title')中心詳情@endsection

@section('ownCSS')
    {{-- Bootstrap Icons --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    {{-- 中心驗證系統樣式 --}}
    <link rel="stylesheet" href="/static/admin/css/center-validation.css">
    <style>
        .center-info-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 1.5rem;
        }
        .center-info-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 1.25rem;
            font-weight: 600;
        }
        .center-info-body {
            padding: 1.25rem;
        }
        .info-row {
            display: flex;
            margin-bottom: 0.75rem;
        }
        .info-label {
            font-weight: 600;
            min-width: 120px;
            color: #495057;
        }
        .info-value {
            flex: 1;
            color: #212529;
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .role-section {
            margin-bottom: 2rem;
        }
        .role-header {
            background-color: #e9ecef;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem 0.375rem 0 0;
            font-weight: 600;
            border: 1px solid #dee2e6;
            border-bottom: none;
        }
        .role-content {
            border: 1px solid #dee2e6;
            border-radius: 0 0 0.375rem 0.375rem;
            padding: 1rem;
            background-color: #fff;
        }
        .staff-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border: 1px solid #e9ecef;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
            background-color: #f8f9fa;
        }
        .staff-info {
            flex: 1;
        }
        .staff-name {
            font-weight: 600;
            color: #212529;
        }
        .staff-details {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        .staff-period {
            font-size: 0.875rem;
            color: #495057;
            text-align: right;
        }
        .no-staff {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 2rem;
        }
        .history-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .history-table th,
        .history-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .history-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .history-table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .period-active {
            color: #28a745;
            font-weight: 600;
        }
        .period-ended {
            color: #6c757d;
        }
        .action-buttons {
            margin-top: 1.5rem;
        }
        .btn-group .btn {
            margin-right: 0.5rem;
        }
    </style>
@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">中心管理</a></li>
            <li><a href="###" onclick="javascript:location.href='/order/center/index'">中心列表</a></li>
            <li><a href="###">中心詳情</a></li>
        </ul>

        <!-- 中心基本資訊 -->
        <div class="center-info-card">
            <div class="center-info-header">
                <i class="bi bi-building"></i> 中心基本資訊
            </div>
            <div class="center-info-body">
                <div class="info-row">
                    <div class="info-label">中心名稱：</div>
                    <div class="info-value">{{ $data['center']->name }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">中心等級：</div>
                    <div class="info-value">{{ $data['center']->level_name ?? '未設定' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">狀態：</div>
                    <div class="info-value">
                        <span class="status-badge {{ $data['center']->status ? 'status-active' : 'status-inactive' }}">
                            {{ $data['center']->status ? '啟用' : '停用' }}
                        </span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">建立時間：</div>
                    <div class="info-value">{{ $data['center']->created_at ?? '未設定' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">更新時間：</div>
                    <div class="info-value">{{ $data['center']->updated_at ?? '未設定' }}</div>
                </div>
            </div>
        </div>

        <!-- 現役人員角色分配 -->
        <div class="center-info-card">
            <div class="center-info-header">
                <i class="bi bi-people"></i> 現役人員角色分配
            </div>
            <div class="center-info-body">
                @php
                    $roleGroups = $data['center']->activeStaff->groupBy('role.code');
                    $allRoles = [
                        'founder' => '中心發起人',
                        'director' => '中心總監',
                        'executive_director' => '大總監',
                        'lecturer' => '講師',
                        'market' => '行政/廣告人員',
                        'sales' => '業務'
                    ];
                @endphp

                @foreach($allRoles as $roleCode => $roleName)
                    <div class="role-section">
                        <div class="role-header">
                            {{ $roleName }}
                            @if(isset($roleGroups[$roleCode]))
                                <span class="badge badge-primary ml-2">{{ $roleGroups[$roleCode]->count() }} 人</span>
                            @else
                                <span class="badge badge-secondary ml-2">0 人</span>
                            @endif
                        </div>
                        <div class="role-content">
                            @if(isset($roleGroups[$roleCode]) && $roleGroups[$roleCode]->count() > 0)
                                @foreach($roleGroups[$roleCode] as $staff)
                                    <div class="staff-item">
                                        <div class="staff-info">
                                            <div class="staff-name">
                                                {{ $staff->account->number ?? '未知' }}
                                                @if($staff->account->name)
                                                    ({{ $staff->account->name }})
                                                @endif
                                            </div>
                                            <div class="staff-details">
                                                @if($staff->note)
                                                    備註：{{ $staff->note }}
                                                @endif
                                            </div>
                                        </div>
                                        <div class="staff-period">
                                            <div class="period-active">
                                                開始：{{ $staff->start_at ? $staff->start_at->format('Y-m-d') : '未設定' }}
                                            </div>
                                            <div class="period-active">
                                                現役中
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="no-staff">
                                    目前沒有擔任此角色的人員
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- 角色歷史記錄 -->
        <div class="center-info-card">
            <div class="center-info-header">
                <i class="bi bi-clock-history"></i> 角色歷史記錄
            </div>
            <div class="center-info-body">
                @if($data['center']->staff->count() > 0)
                    <table class="history-table">
                        <thead>
                            <tr>
                                <th>會員編號</th>
                                <th>姓名</th>
                                <th>角色</th>
                                <th>開始時間</th>
                                <th>結束時間</th>
                                <th>狀態</th>
                                <th>備註</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($data['center']->staff->sortByDesc('start_at') as $staff)
                                <tr>
                                    <td>{{ $staff->account->number ?? '未知' }}</td>
                                    <td>{{ $staff->account->name ?? '-' }}</td>
                                    <td>{{ $staff->role->name ?? '未知角色' }}</td>
                                    <td>{{ $staff->start_at ? $staff->start_at->format('Y-m-d H:i') : '-' }}</td>
                                    <td>
                                        @if($staff->end_at)
                                            {{ $staff->end_at->format('Y-m-d H:i') }}
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        @if($staff->end_at)
                                            <span class="period-ended">已結束</span>
                                        @else
                                            <span class="period-active">現役中</span>
                                        @endif
                                    </td>
                                    <td>{{ $staff->note ?? '-' }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @else
                    <div class="no-staff">
                        目前沒有任何角色歷史記錄
                    </div>
                @endif
            </div>
        </div>

        <!-- 操作按鈕 -->
        <div class="action-buttons">
            <div class="btn-group">
                <a href="/order/center/edit?id={{ $data['center']->id }}" class="btn btn-primary">
                    <i class="bi bi-pencil"></i> 編輯中心
                </a>
                <a href="/order/center/index" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回列表
                </a>
                @if($data['center']->activeStaff->count() == 0)
                    <button type="button" class="btn btn-danger" onclick="confirmDelete({{ $data['center']->id }})">
                        <i class="bi bi-trash"></i> 刪除中心
                    </button>
                @else
                    <button type="button" class="btn btn-danger" disabled title="中心有現役人員，無法刪除">
                        <i class="bi bi-trash"></i> 刪除中心
                    </button>
                @endif
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    <script>
        function confirmDelete(centerId) {
            if (confirm('確定要刪除此中心嗎？此操作無法復原。')) {
                // 建立表單並提交刪除請求
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/order/center/destroy';

                // 添加 CSRF token
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);

                // 添加中心 ID
                const centerIdInput = document.createElement('input');
                centerIdInput.type = 'hidden';
                centerIdInput.name = 'id';
                centerIdInput.value = centerId;
                form.appendChild(centerIdInput);

                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
@endsection
