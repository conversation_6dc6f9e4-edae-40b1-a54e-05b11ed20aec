@extends('admin.Public.aside')
@section('title')中心詳情@endsection

@section('ownCSS')
    {{-- Bootstrap Icons --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    {{-- jQuery UI CSS for autocomplete --}}
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
    <style>
        .center-info-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 1.5rem;
        }
        .center-info-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem 1.25rem;
            font-weight: 600;
        }
        .center-info-body {
            padding: 1.25rem;
        }
        .info-row {
            display: flex;
            margin-bottom: 0.75rem;
        }
        .info-label {
            font-weight: 600;
            min-width: 120px;
            color: #495057;
        }
        .info-value {
            flex: 1;
            color: #212529;
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .role-section {
            margin-bottom: 2rem;
        }
        .role-header {
            background-color: #e9ecef;
            padding: 0.75rem 1rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        .staff-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
        }
        .staff-info {
            flex: 1;
        }
        .staff-name {
            font-weight: 600;
            color: #212529;
        }
        .staff-details {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        .no-staff {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 2rem;
        }
        .assign-form {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
            margin-top: 1rem;
        }
        .form-row {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .form-group {
            flex: 1;
        }
        .ui-autocomplete {
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
        }
    </style>
@endsection

@section('content')
    <div id="content">
        <ul id="title" class="brand-menu">
            <li><a href="###">中心管理</a></li>
            <li><a href="###" onclick="javascript:location.href='/order/center/index'">中心列表</a></li>
            <li><a href="###">中心詳情</a></li>
        </ul>

        <!-- 中心基本資訊 -->
        <div class="center-info-card">
            <div class="center-info-header">
                <i class="bi bi-building"></i> 中心基本資訊
            </div>
            <div class="center-info-body">
                <div class="info-row">
                    <div class="info-label">中心名稱：</div>
                    <div class="info-value">{{ $data['center']->name }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">中心等級：</div>
                    <div class="info-value">{{ $data['center']->level_name ?? '未設定' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">狀態：</div>
                    <div class="info-value">
                        <span class="status-badge {{ $data['center']->status ? 'status-active' : 'status-inactive' }}">
                            {{ $data['center']->status ? '啟用' : '停用' }}
                        </span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">建立時間：</div>
                    <div class="info-value">{{ $data['center']->created_at ?? '未設定' }}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">更新時間：</div>
                    <div class="info-value">{{ $data['center']->updated_at ?? '未設定' }}</div>
                </div>
            </div>
        </div>

        <!-- 現役人員角色分配 -->
        <div class="center-info-card">
            <div class="center-info-header">
                <i class="bi bi-people"></i> 現役人員角色分配
            </div>
            <div class="center-info-body">
                @php
                    $roleGroups = collect($data['center_staff'])->groupBy('role_code');
                    $allRoles = [
                        'founder' => '中心發起人',
                        'director' => '中心總監',
                        'executive_director' => '大總監',
                        'lecturer' => '講師',
                        'business' => '業務',
                        'admin_ad' => '行政廣告人員'
                    ];
                @endphp

                @foreach($allRoles as $roleCode => $roleName)
                    <div class="role-section">
                        <div class="role-header">
                            {{ $roleName }}
                            @if(in_array($roleCode, ['founder', 'director', 'executive_director']))
                                <span class="badge badge-info">單例角色</span>
                            @else
                                <span class="badge badge-secondary">多人角色</span>
                            @endif
                        </div>

                        @if($roleGroups->has($roleCode))
                            @foreach($roleGroups[$roleCode] as $staff)
                                <div class="staff-item">
                                    <div class="staff-info">
                                        <div class="staff-name">{{ $staff->account_name ?? '未知' }}</div>
                                        <div class="staff-details">
                                            編號：{{ $staff->account_number ?? '未知' }}
                                            @if($staff->account_email)
                                                | 信箱：{{ $staff->account_email }}
                                            @endif
                                            | 開始時間：{{ $staff->start_at ?? '未知' }}
                                        </div>
                                    </div>
                                    <div class="staff-actions">
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeRole({{ $staff->id }})">
                                            <i class="bi bi-x"></i> 移除
                                        </button>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="no-staff">
                                尚未指派此角色
                            </div>
                        @endif

                        <!-- 角色指派表單 -->
                        <div class="assign-form">
                            <h6>指派 {{ $roleName }}</h6>
                            <form class="assign-role-form" data-role-code="{{ $roleCode }}">
                                @csrf
                                <input type="hidden" name="center_id" value="{{ $data['center']->id }}">
                                <input type="hidden" name="role_code" value="{{ $roleCode }}">

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="account_number_{{ $roleCode }}">會員編號</label>
                                        <input type="text"
                                               class="form-control account-autocomplete"
                                               id="account_number_{{ $roleCode }}"
                                               name="account_number"
                                               placeholder="輸入會員編號或姓名搜尋"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label for="note_{{ $roleCode }}">備註</label>
                                        <input type="text"
                                               class="form-control"
                                               id="note_{{ $roleCode }}"
                                               name="note"
                                               placeholder="選填">
                                    </div>
                                    <div class="form-group" style="display: flex; align-items: end;">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus"></i> 指派
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- 操作按鈕 -->
        <div class="center-info-card">
            <div class="center-info-body">
                <div class="btn-group">
                    <a href="/order/center/edit?id={{ $data['center']->id }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> 編輯中心
                    </a>
                    <a href="/order/center/index" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('ownJS')
    {{-- jQuery UI JavaScript for autocomplete --}}
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

    <script>
        $(document).ready(function() {
            // 設置CSRF token
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // 初始化會員編號自動完成
            $('.account-autocomplete').autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: '/order/center/account-suggestions',
                        data: {
                            query: request.term,
                            limit: 10
                        },
                        success: function(data) {
                            if (data.status) {
                                response(data.data);
                            } else {
                                response([]);
                            }
                        },
                        error: function() {
                            response([]);
                        }
                    });
                },
                minLength: 2,
                select: function(event, ui) {
                    $(this).val(ui.item.value);
                    return false;
                }
            });

            // 處理角色指派表單提交
            $('.assign-role-form').on('submit', function(e) {
                e.preventDefault();

                const form = $(this);
                const submitBtn = form.find('button[type="submit"]');
                const originalText = submitBtn.html();
                const accountNumber = form.find('input[name="account_number"]').val();

                if (!accountNumber) {
                    alert('請輸入會員編號');
                    return;
                }

                // 顯示載入狀態
                submitBtn.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> 指派中...');

                // 直接提交角色指派
                $.ajax({
                    url: '/order/center/assign-role',
                    method: 'POST',
                    data: form.serialize(),
                    success: function(response) {
                        if (response.status) {
                            alert('角色指派成功！');
                            location.reload();
                        } else {
                            alert('指派失敗：' + response.message);
                        }
                    },
                    error: function(xhr) {
                        let message = '指派失敗';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        alert(message);
                    },
                    complete: function() {
                        submitBtn.prop('disabled', false).html(originalText);
                    }
                });
            });
        });

        // 移除角色
        function removeRole(staffId) {
            if (confirm('確定要移除此角色指派嗎？')) {
                $.ajax({
                    url: '/order/center/remove-role',
                    method: 'POST',
                    data: {
                        staff_id: staffId,
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.status) {
                            alert('角色移除成功！');
                            location.reload();
                        } else {
                            alert('移除失敗：' + response.message);
                        }
                    },
                    error: function(xhr) {
                        let message = '移除失敗';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            message = xhr.responseJSON.message;
                        }
                        alert(message);
                    }
                });
            }
        }
    </script>
@endsection
